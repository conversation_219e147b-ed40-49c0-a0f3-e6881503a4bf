{"version": 3, "file": "onErrorResumeNext.js", "sources": ["../../src/internal/operators/onErrorResumeNext.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,2CAA0C;AAI1C,2CAA0C;AAC1C,sDAAqD;AACrD,sDAAqD;AACrD,+DAA8D;AAkF9D,SAAgB,iBAAiB;IAAO,qBAE8C;SAF9C,UAE8C,EAF9C,qBAE8C,EAF9C,IAE8C;QAF9C,gCAE8C;;IACpF,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,iBAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;QACvD,WAAW,GAA2B,WAAW,CAAC,CAAC,CAAC,CAAC;KACtD;IAED,OAAO,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,yBAAyB,CAAO,WAAW,CAAC,CAAC,EAA7D,CAA6D,CAAC;AAClG,CAAC;AARD,8CAQC;AAaD,SAAgB,uBAAuB;IAAO,qBAE+C;SAF/C,UAE+C,EAF/C,qBAE+C,EAF/C,IAE+C;QAF/C,gCAE+C;;IAC3F,IAAI,MAAM,GAAyB,IAAI,CAAC;IAExC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,iBAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;QACvD,WAAW,GAAgC,WAAW,CAAC,CAAC,CAAC,CAAC;KAC3D;IACD,MAAM,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC;IAE7B,OAAO,WAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,yBAAyB,CAAO,WAAW,CAAC,CAAC,CAAC;AACnF,CAAC;AAXD,0DAWC;AAED;IACE,mCAAoB,WAAwC;QAAxC,gBAAW,GAAX,WAAW,CAA6B;IAC5D,CAAC;IAED,wCAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,2BAA2B,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACzF,CAAC;IACH,gCAAC;AAAD,CAAC,AAPD,IAOC;AAED;IAAgD,+CAAqB;IACnE,qCAAsB,WAA0B,EAC5B,WAAwC;QAD5D,YAEE,kBAAM,WAAW,CAAC,SACnB;QAHqB,iBAAW,GAAX,WAAW,CAAe;QAC5B,iBAAW,GAAX,WAAW,CAA6B;;IAE5D,CAAC;IAED,iDAAW,GAAX,UAAY,KAAU,EAAE,QAAiC;QACvD,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAED,oDAAc,GAAd,UAAe,QAAiC;QAC9C,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAES,4CAAM,GAAhB,UAAiB,GAAQ;QACvB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAES,+CAAS,GAAnB;QACE,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAEO,2DAAqB,GAA7B;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,CAAC,IAAI,EAAE;YACV,IAAM,eAAe,GAAG,IAAI,iCAAe,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;YACxE,IAAM,WAAW,GAAG,IAAI,CAAC,WAA2B,CAAC;YACrD,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YACjC,qCAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;SACtE;aAAM;YACL,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;SAC7B;IACH,CAAC;IACH,kCAAC;AAAD,CAAC,AAnCD,CAAgD,iCAAe,GAmC9D"}