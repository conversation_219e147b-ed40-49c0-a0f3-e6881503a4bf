{"version": 3, "file": "Subscription.js", "sources": ["../src/internal/Subscription.ts"], "names": [], "mappings": ";;AAAA,0CAAyC;AACzC,4CAA2C;AAC3C,gDAA+C;AAC/C,kEAAiE;AAejE;IAwBE,sBAAY,WAAwB;QAb7B,WAAM,GAAY,KAAK,CAAC;QAGrB,YAAO,GAAiB,IAAI,CAAC;QAE7B,aAAQ,GAAmB,IAAI,CAAC;QAElC,mBAAc,GAAuB,IAAI,CAAC;QAOhD,IAAI,WAAW,EAAE;YACR,IAAK,CAAC,YAAY,GAAG,WAAW,CAAC;SACzC;IACH,CAAC;IAQD,kCAAW,GAAX;QACE,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,MAAa,CAAC;QAElB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO;SACR;QAEG,IAAA,SAAkE,EAAhE,oBAAO,EAAE,sBAAQ,EAAE,8BAAY,EAAE,kCAAc,CAAkB;QAEvE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAGrB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;QACf,IAAI,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAIzC,OAAO,OAAO,EAAE;YACd,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAGrB,OAAO,GAAG,EAAE,KAAK,GAAG,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;SACpD;QAED,IAAI,uBAAU,CAAC,YAAY,CAAC,EAAE;YAC5B,IAAI;gBACF,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACzB;YAAC,OAAO,CAAC,EAAE;gBACV,SAAS,GAAG,IAAI,CAAC;gBACjB,MAAM,GAAG,CAAC,YAAY,yCAAmB,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACzF;SACF;QAED,IAAI,iBAAO,CAAC,cAAc,CAAC,EAAE;YAE3B,KAAK,GAAG,CAAC,CAAC,CAAC;YACX,GAAG,GAAG,cAAc,CAAC,MAAM,CAAC;YAE5B,OAAO,EAAE,KAAK,GAAG,GAAG,EAAE;gBACpB,IAAM,GAAG,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;gBAClC,IAAI,mBAAQ,CAAC,GAAG,CAAC,EAAE;oBACjB,IAAI;wBACF,GAAG,CAAC,WAAW,EAAE,CAAC;qBACnB;oBAAC,OAAO,CAAC,EAAE;wBACV,SAAS,GAAG,IAAI,CAAC;wBACjB,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;wBACtB,IAAI,CAAC,YAAY,yCAAmB,EAAE;4BACpC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;yBAC/D;6BAAM;4BACL,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;yBAChB;qBACF;iBACF;aACF;SACF;QAED,IAAI,SAAS,EAAE;YACb,MAAM,IAAI,yCAAmB,CAAC,MAAM,CAAC,CAAC;SACvC;IACH,CAAC;IAsBD,0BAAG,GAAH,UAAI,QAAuB;QACzB,IAAI,YAAY,GAAkB,QAAS,CAAC;QAC5C,QAAQ,OAAO,QAAQ,EAAE;YACvB,KAAK,UAAU;gBACb,YAAY,GAAG,IAAI,YAAY,CAAe,QAAQ,CAAC,CAAC;YAC1D,KAAK,QAAQ;gBACX,IAAI,YAAY,KAAK,IAAI,IAAI,YAAY,CAAC,MAAM,IAAI,OAAO,YAAY,CAAC,WAAW,KAAK,UAAU,EAAE;oBAElG,OAAO,YAAY,CAAC;iBACrB;qBAAM,IAAI,IAAI,CAAC,MAAM,EAAE;oBACtB,YAAY,CAAC,WAAW,EAAE,CAAC;oBAC3B,OAAO,YAAY,CAAC;iBACrB;qBAAM,IAAI,CAAC,CAAC,YAAY,YAAY,YAAY,CAAC,EAAE;oBAClD,IAAM,GAAG,GAAG,YAAY,CAAC;oBACzB,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;oBAClC,YAAY,CAAC,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;iBACrC;gBACD,MAAM;YACR,OAAO,CAAC,CAAC;gBACP,IAAI,CAAO,QAAS,EAAE;oBACpB,OAAO,YAAY,CAAC,KAAK,CAAC;iBAC3B;gBACD,MAAM,IAAI,KAAK,CAAC,wBAAwB,GAAG,QAAQ,GAAG,yBAAyB,CAAC,CAAC;aAClF;SACF;QAED,IAAI,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YAEjC,IAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;YAC1C,IAAI,aAAa,EAAE;gBACjB,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aAClC;iBAAM;gBACL,IAAI,CAAC,cAAc,GAAG,CAAC,YAAY,CAAC,CAAC;aACtC;SACF;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAQD,6BAAM,GAAN,UAAO,YAA0B;QAC/B,IAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAC1C,IAAI,aAAa,EAAE;YACjB,IAAM,iBAAiB,GAAG,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAC9D,IAAI,iBAAiB,KAAK,CAAC,CAAC,EAAE;gBAC5B,aAAa,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;aAC5C;SACF;IACH,CAAC;IAGO,iCAAU,GAAlB,UAAmB,MAAoB;QACjC,IAAA,SAA4B,EAA1B,oBAAO,EAAE,sBAAQ,CAAU;QACjC,IAAI,OAAO,KAAK,MAAM,EAAE;YAEtB,OAAO,KAAK,CAAC;SACd;aAAM,IAAI,CAAC,OAAO,EAAE;YAEnB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,OAAO,IAAI,CAAC;SACb;aAAM,IAAI,CAAC,QAAQ,EAAE;YAGpB,IAAI,CAAC,QAAQ,GAAG,CAAC,MAAM,CAAC,CAAC;YACzB,OAAO,IAAI,CAAC;SACb;aAAM,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;YAE1C,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtB,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IApMa,kBAAK,GAAiB,CAAC,UAAS,KAAU;QACtD,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,IAAI,YAAY,EAAE,CAAC,CAAC,CAAC;IAkMzB,mBAAC;CAAA,AAvMD,IAuMC;AAvMY,oCAAY;AAyMzB,SAAS,2BAA2B,CAAC,MAAa;IACjD,OAAO,MAAM,CAAC,MAAM,CAAC,UAAC,IAAI,EAAE,GAAG,IAAK,OAAA,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,YAAY,yCAAmB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAApE,CAAoE,EAAE,EAAE,CAAC,CAAC;AAC/G,CAAC"}