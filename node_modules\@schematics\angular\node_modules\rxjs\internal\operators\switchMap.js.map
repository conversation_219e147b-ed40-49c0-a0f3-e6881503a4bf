{"version": 3, "file": "switchMap.js", "sources": ["../../src/internal/operators/switchMap.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAIA,sDAAqD;AACrD,sDAAqD;AACrD,+DAA8D;AAE9D,6BAA4B;AAC5B,2CAA0C;AAuD1C,SAAgB,SAAS,CACvB,OAAuC,EACvC,cAA6G;IAE7G,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE;QACxC,OAAO,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAC3C,SAAS,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,WAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAC1C,SAAG,CAAC,UAAC,CAAC,EAAE,EAAE,IAAK,OAAA,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAA3B,CAA2B,CAAC,CAC5C,EAFmB,CAEnB,CAAC,CACH,EAJiC,CAIjC,CAAC;KACH;IACD,OAAO,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,OAAO,CAAC,CAAC,EAA3C,CAA2C,CAAC;AAChF,CAAC;AAZD,8BAYC;AAED;IACE,2BAAoB,OAAwD;QAAxD,YAAO,GAAP,OAAO,CAAiD;IAC5E,CAAC;IAED,gCAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAC7E,CAAC;IACH,wBAAC;AAAD,CAAC,AAPD,IAOC;AAOD;IAAwC,uCAAqB;IAI3D,6BAAY,WAA0B,EAClB,OAAwD;QAD5E,YAEE,kBAAM,WAAW,CAAC,SACnB;QAFmB,aAAO,GAAP,OAAO,CAAiD;QAJpE,WAAK,GAAW,CAAC,CAAC;;IAM1B,CAAC;IAES,mCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,MAA0B,CAAC;QAC/B,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI;YACF,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACrC;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9B,OAAO;SACR;QACD,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACvC,CAAC;IAEO,uCAAS,GAAjB,UAAkB,MAA0B,EAAE,KAAQ,EAAE,KAAa;QACnE,IAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACjD,IAAI,iBAAiB,EAAE;YACrB,iBAAiB,CAAC,WAAW,EAAE,CAAC;SACjC;QACD,IAAM,eAAe,GAAG,IAAI,iCAAe,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QACxE,IAAM,WAAW,GAAG,IAAI,CAAC,WAA2B,CAAC;QACrD,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,qCAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;IAC1F,CAAC;IAES,uCAAS,GAAnB;QACS,IAAA,0CAAiB,CAAS;QACjC,IAAI,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,EAAE;YAClD,iBAAM,SAAS,WAAE,CAAC;SACnB;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAES,0CAAY,GAAtB;QACE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAChC,CAAC;IAED,4CAAc,GAAd,UAAe,QAAsB;QACnC,IAAM,WAAW,GAAG,IAAI,CAAC,WAA2B,CAAC;QACrD,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC7B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,iBAAM,SAAS,WAAE,CAAC;SACnB;IACH,CAAC;IAED,wCAAU,GAAV,UAAW,UAAa,EAAE,UAAa,EAC5B,UAAkB,EAAE,UAAkB,EACtC,QAA+B;QACtC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACtC,CAAC;IACH,0BAAC;AAAD,CAAC,AA1DD,CAAwC,iCAAe,GA0DtD"}