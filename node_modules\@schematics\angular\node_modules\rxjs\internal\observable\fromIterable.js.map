{"version": 3, "file": "fromIterable.js", "sources": ["../../src/internal/observable/fromIterable.ts"], "names": [], "mappings": ";;AAAA,4CAA2C;AAE3C,gDAA+C;AAC/C,+CAAiE;AACjE,mEAAkE;AAElE,SAAgB,YAAY,CAAI,KAAkB,EAAE,SAAwB;IAC1E,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;KAC5C;IACD,IAAI,CAAC,SAAS,EAAE;QACd,OAAO,IAAI,uBAAU,CAAI,yCAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;KACtD;SAAM;QACL,OAAO,IAAI,uBAAU,CAAI,UAAA,UAAU;YACjC,IAAM,GAAG,GAAG,IAAI,2BAAY,EAAE,CAAC;YAC/B,IAAI,QAAqB,CAAC;YAC1B,GAAG,CAAC,GAAG,CAAC;gBAEN,IAAI,QAAQ,IAAI,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU,EAAE;oBACrD,QAAQ,CAAC,MAAM,EAAE,CAAC;iBACnB;YACH,CAAC,CAAC,CAAC;YACH,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;gBACzB,QAAQ,GAAG,KAAK,CAAC,mBAAe,CAAC,EAAE,CAAC;gBACpC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;oBACzB,IAAI,UAAU,CAAC,MAAM,EAAE;wBACrB,OAAO;qBACR;oBACD,IAAI,KAAQ,CAAC;oBACb,IAAI,IAAa,CAAC;oBAClB,IAAI;wBACF,IAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;wBAC/B,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;wBACrB,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;qBACpB;oBAAC,OAAO,GAAG,EAAE;wBACZ,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;wBACtB,OAAO;qBACR;oBACD,IAAI,IAAI,EAAE;wBACR,UAAU,CAAC,QAAQ,EAAE,CAAC;qBACvB;yBAAM;wBACL,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACvB,IAAI,CAAC,QAAQ,EAAE,CAAC;qBACjB;gBACH,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC;YACJ,OAAO,GAAG,CAAC;QACb,CAAC,CAAC,CAAC;KACJ;AACH,CAAC;AA3CD,oCA2CC"}