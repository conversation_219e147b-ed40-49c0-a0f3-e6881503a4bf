{"version": 3, "file": "fromPromise.js", "sources": ["../../src/internal/observable/fromPromise.ts"], "names": [], "mappings": ";;AAAA,4CAA2C;AAE3C,gDAA+C;AAC/C,iEAAgE;AAEhE,SAAgB,WAAW,CAAI,KAAqB,EAAE,SAAyB;IAC7E,IAAI,CAAC,SAAS,EAAE;QACd,OAAO,IAAI,uBAAU,CAAI,uCAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;KACrD;SAAM;QACL,OAAO,IAAI,uBAAU,CAAI,UAAA,UAAU;YACjC,IAAM,GAAG,GAAG,IAAI,2BAAY,EAAE,CAAC;YAC/B,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,cAAM,OAAA,KAAK,CAAC,IAAI,CACzC,UAAA,KAAK;gBACH,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;oBACzB,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACvB,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,cAAM,OAAA,UAAU,CAAC,QAAQ,EAAE,EAArB,CAAqB,CAAC,CAAC,CAAC;gBAC3D,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,EACD,UAAA,GAAG;gBACD,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,cAAM,OAAA,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,EAArB,CAAqB,CAAC,CAAC,CAAC;YAC3D,CAAC,CACF,EAVgC,CAUhC,CAAC,CAAC,CAAC;YACJ,OAAO,GAAG,CAAC;QACb,CAAC,CAAC,CAAC;KACJ;AACH,CAAC;AApBD,kCAoBC"}